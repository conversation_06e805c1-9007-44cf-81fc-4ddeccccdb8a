// Simple test to verify game functionality
// This can be run in the browser console

function testGameLogic() {
  console.log('Testing Swoop game logic...');
  
  // Test lane configuration
  const LANES = [
    {sum:2, L:3, basket:true},
    {sum:3, L:4, basket:false},
    {sum:4, L:5, basket:true},
    {sum:5, L:6, basket:false},
    {sum:6, L:7, basket:true},
    {sum:7, L:8, basket:false},
    {sum:8, L:7, basket:true},
    {sum:9, L:6, basket:false},
    {sum:10, L:5, basket:true},
    {sum:11, L:4, basket:false},
    {sum:12, L:3, basket:true},
  ];
  
  console.log('✓ Lane configuration correct:', LANES.length === 11);
  
  // Test checkpoints function
  function checkpoints(L){ 
    const out=[2]; 
    if(L>=6) out.push(4); 
    out.push(L-1); 
    return [...new Set(out)].filter(x=>x>=1&&x<=L); 
  }
  
  console.log('✓ Checkpoints for L=7:', checkpoints(7)); // Should be [2, 4, 6]
  console.log('✓ Checkpoints for L=3:', checkpoints(3)); // Should be [2]
  
  // Test deterrents function
  function deterrents(L,sum){ 
    if(L<=3) return []; 
    const det=[3,L-2]; 
    if((sum===6||sum===8)&&L>=5) det.push(5); 
    const cps=checkpoints(L); 
    return [...new Set(det)].filter(x=>x>=1&&x<=L && !cps.includes(x)); 
  }
  
  console.log('✓ Deterrents for L=7, sum=6:', deterrents(7, 6)); // Should include deterrents
  
  // Test grid positioning
  const COLS = 27;
  const CENTER_COL = 13;
  const LEFT_START_COL = 1;
  const RIGHT_END_COL = COLS - 2;
  const LEFT_SPAN = CENTER_COL - LEFT_START_COL - 1;
  const RIGHT_SPAN = RIGHT_END_COL - CENTER_COL - 1;

  function colForStep(side, step, L) {
    if (side === 'L') {
      const rel = Math.round((LEFT_SPAN - 1) * (step - 1) / (L - 1));
      return LEFT_START_COL + rel;
    }
    const rel = Math.round((RIGHT_SPAN - 1) * (step - 1) / (L - 1));
    return RIGHT_END_COL - rel;
  }
  
  console.log('✓ Grid positioning for L side, step 1, L=7:', colForStep('L', 1, 7));
  console.log('✓ Grid positioning for R side, step 1, L=7:', colForStep('R', 1, 7));
  
  console.log('All basic game logic tests passed! ✓');
}

// Run the test
if (typeof window !== 'undefined') {
  // Browser environment
  window.testGameLogic = testGameLogic;
  console.log('Test function available as window.testGameLogic()');
} else {
  // Node environment
  testGameLogic();
}
